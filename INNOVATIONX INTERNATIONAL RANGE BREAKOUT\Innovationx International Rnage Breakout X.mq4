//+------------------------------------------------------------------+
//|                 Innovationx Range Master Pro.mq4                  |
//|      Professional Range Breakout Trading System for MT4          |
//|      Designed by <PERSON>                          |
//|      Contact: +49 1521 6294394                                   |
//|      Copyright 2025, Innovationx International                   |
//|      https://innovationxinternational.com                        |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Innovationx International"
#property link      "https://innovationxinternational.com"
#property version   "2.0"
#property strict
#property description "Professional Range Breakout Trading System"

// ================================
// Trade Sizing & Risk Management
// ================================
input string SECTION_1 = "--- Trade Sizing & Risk Management ---\nConfigure how much to trade, risk per trade, and position sizing method.";
input double LotSize = 0.01;                // Default trading lot size for all trades unless overridden.
input double InitialTradeRiskPercent = 0;   // % of balance to risk on the first trade (0 = use min lot).
input bool UseFixedRisk = false;            // Use a fixed money risk per trade (true/false).
input double RiskAmount = 17.0;             // Amount of money to risk per trade (if fixed risk).
input double RiskPercent = 1.0;             // Percentage risk per trade (if using percent risk).
input bool UsePercentRisk = false;          // Use percent risk instead of fixed amount (true/false).
input double PointValue = 0.20;             // Value of 1 point in account currency.
input int MaxTrades = 1;                    // Max trades per direction (open at any time).
input int MaxDailyTradesCount = 0;          // Max total trades per day (0 = unlimited).

// ================================
// Profit Targets
// ================================
input string SECTION_2 = "--- Profit Targets ---\nSet your take profit rules for each trade.";
input double TradeTargetR = 1.5;            // Take profit as a multiple of stop loss (R-multiple).
input double TradeTargetPoints = 0;         // Take profit in points (overrides R if >0).

// ================================
// Stop Loss & Protection
// ================================
input string SECTION_3 = "--- Stop Loss & Protection ---\nConfigure stop loss rules and additional protection.";
enum StopLossType { SL_RANGE, SL_POINTS, SL_PRICE, SL_MIDRANGE };
input StopLossType StopLossMethod = SL_RANGE; // Method for setting stop loss.
input bool UseRangeStopLoss = true;         // Use range boundaries as stop loss (true/false). (Deprecated: Use StopLossMethod)
input int AddedStopLoss = 0;                // Additional points for stop loss (used with SL_RANGE and SL_MIDRANGE).
input int FixedStopLossPoints = 0;          // Fixed stop loss distance in points (used with SL_POINTS).
input double FixedStopLossPrice = 0.0;      // Fixed stop loss price level (used with SL_PRICE).

// ================================
// Breakeven Logic
// ================================
input string SECTION_4 = "--- Breakeven Logic ---\nConfigure when and how to move stops to breakeven.";
input bool UseBreakEven = true;             // Move to breakeven when in profit (true/false).
input int BreakEvenPoints = 2500;           // Points before breakeven.
input bool UseBreakEvenPercent = false;     // Use percentage for breakeven trigger (true/false).
input double BreakEvenPercent = 0.0;        // Percentage profit before breakeven triggers (e.g., 1.0 for 1%).
input int BETriggerPoints = 0;              // (Alternative) Points before breakeven triggers: set only one.
input double BETriggerPercent = 0;          // (Alternative) % profit before breakeven triggers: set only one.

// ================================
// Trailing Stop Management
// ================================
input string SECTION_5 = "--- Trailing Stop Management ---\nConfigure trailing stop rules to lock in profits.";
input bool UseTrailingStop = true;          // Use trailing stop (true/false).
input int TrailingStopPoints = 5000;        // Points for trailing stop distance.
input bool UseTrailingStopPercent = false;  // Use percentage for trailing stop distance (true/false).
input double TrailingStopPercent = 0.0;     // Percentage distance for trailing stop (e.g., 1.0 for 1%).
// (Add any other trailing stop parameters here if present)

// ================================
// Range & Session Timing
// ================================
input string SECTION_6 = "--- Range & Session Timing ---\nDefine when the range is measured and which market session to trade.";
input int RangeStartHour = 0;              // When the range starts (hour, e.g., 23 for 11:00 PM).
input int RangeStartMinute = 0;             // When the range starts (minute).
input int RangeEndHour = 6;                // When the range ends (hour, e.g., 18 for 6:00 PM).
input int RangeEndMinute = 0;               // When the range ends (minute).
input int SessionPreset = 0;                // Preset for trading session (e.g., 0=24hr, 1=London, etc.).
input int CustomSessionStartHour = 0;       // Custom session start hour.
input int CustomSessionEndHour = 23;        // Custom session end hour.

// ================================
// Entry Buffer & Filtering
// ================================
input string SECTION_7 = "--- Entry Buffer & Filtering ---\nFine-tune entry buffer, minimum lot logic, and basic entry filters.";
input bool EnableDynamicEntryBuffer = false; // Use dynamic entry buffer (true/false).
input int EntryBufferPoints = 20;           // Entry buffer in points.
input double EntryBufferPercent = 0.1;      // Entry buffer as percent of range.
input bool AlwaysUseMinLot = false;         // Always use broker minimum lot size (true/false).

// ================================
// Multi-Trade & Per-Trade Settings
// ================================
input string SECTION_8 = "--- Multi-Trade & Per-Trade Settings ---\nConfigure the number of trades per breakout and per-trade parameters.";
input int NumBreakoutTrades = 1;            // Number of breakout trades per side.

// -- Trade 1 --
input double LotSize1 = 0.01;               // Lot size for Trade 1.
input double TP1 = 0;                       // Take profit for Trade 1 (points, 0 = default).
input int SL1 = 0;                          // Stop loss offset for Trade 1 (points, 0 = default).
input int Trade1Rule = 0;                   // 0=Normal, 1=TightSL, 2=BreakevenRunner.
input string Comment1 = "Trade 1";
input color Trade1Color = clrGreen;
// -- Trade 2 --
input double LotSize2 = 0.01;               // Lot size for Trade 2.
input double TP2 = 0;
input int SL2 = 0;
input int Trade2Rule = 0;
input string Comment2 = "Trade 2";
input color Trade2Color = clrBlue;
// -- Trade 3+ --
input double LotSize3 = 0.01;               // Lot size for Trade 3+.
input double TP3 = 0;
input int SL3 = 0;
input int Trade3Rule = 0;
input string Comment3 = "Trade 3+";
input color Trade3Color = clrRed;
input bool EnableStaggeredOrders = false;   // Enable staggered order entries (true/false).
input double StaggeredOrderPercent = 0.2;   // Percent for staggered order offset.
input bool TightSL = false;                 // Enable tight stop loss (true/false).
input int TightSLPoints = 100;              // Points for tight stop loss.
input bool BreakevenRunner = false;         // Enable breakeven runner logic (true/false).
input string SymbolsList = "EURUSD,GBPUSD,USDJPY"; // List of symbols for multi-symbol trading.

// ================================
// Risk & Range Filters
// ================================
input double MinRangePoints = 0;            // No trade if range < this (0 = disabled).
input double MaxRangePoints = 0;            // No trade if range > this (0 = disabled).
input double MaxSpreadPoints = 50;          // No trade if spread > this.
input int PendingOrderExpiryHours = 6;      // Cancel pending if not triggered in X hours.
input double MaxDailyLoss = 4.0;            // Max daily loss percent (default 4%).
input double MaxWeeklyLoss = 10.0;          // Max weekly loss percent (default 10%).
input double EmergencyStopEquityPercent = 80.0; // If equity drops below this % of starting equity, all trades are closed.

// ================================
// Volatility & ADR/ATR Filters
// ================================
input bool EnableATRFilter = false;         // Use ATR filter for range validation (true/false).
input int ATRPeriod = 14;                   // ATR calculation period.
input double MinRangeATR = 0.5;             // Minimum range as ATR multiple.
input double MaxRangeATR = 2.5;             // Maximum range as ATR multiple.
input bool UseADRLimit = false;             // Use ADR-based range filter (true/false).
input int ADRDays = 14;                     // ADR period (days).
input double MaxRangeADRMultiple = 1.5;     // Max allowed range as multiple of ADR.
input int ADRType = 0;                      // 0=Daily ADR, 1=Weekly ADR.

// ================================
// Visuals, Alerts & Panels
// ================================
input string SECTION_9 = "--- Visuals, Alerts & Panels ---\nCustomize chart visuals, enable/disable alerts, and show/hide the stats panel.";
input color RangeBoxColor = clrYellow;      // Color for the range box.
input color RangeTopLineColor = clrRed;     // Color for the top line.
input color RangeBottomLineColor = clrLime; // Color for the bottom line.
input int RangeBoxOpacity = 20;             // Opacity for the box fill (0-255).
input bool EnableStatsPanel = true;         // Show the stats panel (true/false).
input bool EnableAlerts = true;             // Enable alerts (true/false).

// ================================
// Fakeout & Reversal Logic
// ================================
input string SECTION_12 = "--- Fakeout & Reversal Logic ---\nConfigure advanced fakeout detection and reversal entry strategies.";
input bool EnableFakeoutFade = false;       // Use fakeout fade logic (true/false).
input int SessionOpenHour = 9;              // Session open hour for fakeout logic.
input int SessionOpenMinute = 0;            // Session open minute for fakeout logic.
input bool EnableCustomTradingHours = false; // Enable custom trading hours (true/false).
input int CustomTradingStartHour = 8;       // Custom trading session start hour.
input int CustomTradingStartMinute = 0;     // Custom trading session start minute.
input int CustomTradingEndHour = 17;        // Custom trading session end hour.
input int CustomTradingEndMinute = 0;       // Custom trading session end minute.
input bool EnableEnhancedFakeout = false;   // Enable enhanced fakeout logic (true/false).
input bool EnableFakeoutCustomHours = false; // Enable custom trading hours for fakeout logic (true/false).
input int FakeoutStartHour = 0;             // Fakeout session start hour.
input int FakeoutStartMinute = 0;           // Fakeout session start minute.
input int FakeoutEndHour = 23;              // Fakeout session end hour.
input int FakeoutEndMinute = 59;            // Fakeout session end minute.
input bool EnableReversalEntryLogic = false; // Enable/Disable Reversal Entry Logic.
input int MaxDailyReversalTrades = 0;   // Max reversal trades per day (0 = unlimited).

// ================================
// Breakeven & Trailing by Profit Amount
// ================================
input bool UseBreakEvenProfitAmount = false;      // Enable BE by profit amount
input double BreakEvenProfitAmount = 0;          // Profit (in account currency) to trigger BE

input bool UseTrailingProfitAmount = false;       // Enable trailing by profit amount
input double TrailingProfitTrigger = 0;           // Profit (in account currency) to start trailing
input double TrailingProfitStep = 0;              // Trail distance (in account currency) behind profit

//--- ENUMS & TRADE RULE CONSTANTS ---
// Trade rule types for per-trade logic
enum TradeRuleType { RuleNormal = 0, RuleTightSL = 1, RuleBreakevenRunner = 2 };
#define RULE_NORMAL 0
#define RULE_TIGHTSL 1
#define RULE_BREAKEVENRUNNER 2

double CurrentADR = 0;                         // Calculated ADR for info panel
bool ADRTooBig = false;                        // Flag for range too big vs ADR

// Custom Trading Hours Variables
datetime TradingStart = 0;                     // Custom trading session start time
datetime TradingEnd = 0;                       // Custom trading session end time

// Enhanced Fakeout Logic Variables
bool FakeoutBuyOrderPlaced = false;            // Flag to track if fakeout buy order was placed
bool FakeoutSellOrderPlaced = false;           // Flag to track if fakeout sell order was placed
bool FakeoutLogicActive = false;               // Flag to track if fakeout logic is currently active

// Fakeout Trading Hours Variables
datetime FakeoutTradingStart = 0;              // Custom fakeout trading session start time
datetime FakeoutTradingEnd = 0;                // Custom fakeout trading session end time

// Broker lot info (auto-detected)
double BrokerMinLot = 0.01;
double BrokerMaxLot = 100.0;

// Helper: Calculate lot size for risk
// Returns min lot if InitialTradeRiskPercent==0
// Otherwise, calculates lot size for given risk percent
// stopDist in points
// Returns lot size (rounded to broker min lot step)
double CalcInitialLot(double stopDist) {
   double lot = BrokerMinLot;

   // Update broker lot info
   BrokerMinLot = MarketInfo(Symbol(), MODE_MINLOT);
   BrokerMaxLot = MarketInfo(Symbol(), MODE_MAXLOT);
   double step = MarketInfo(Symbol(), MODE_LOTSTEP);

   if(InitialTradeRiskPercent > 0 && stopDist > 0 && AccountBalance() > 0) {
      double riskMoney = AccountBalance() * InitialTradeRiskPercent / 100.0;
      lot = riskMoney / (stopDist * PointValue);

      // Round to broker lot step
      if(step > 0) {
         lot = MathFloor(lot / step) * step;
      }

      // Ensure within broker limits
      lot = MathMax(BrokerMinLot, lot);
      lot = MathMin(lot, BrokerMaxLot);

      Print("[DEBUG] CalcInitialLot: Risk=", InitialTradeRiskPercent, "%, StopDist=", stopDist,
            " pts, RiskMoney=$", riskMoney, ", CalculatedLot=", lot);
   }
   return lot;
}

//+------------------------------------------------------------------+
//| RESEARCH: Best Range Times for Breakouts                         |
//| - London Open: 08:00–09:00 UK (GBP/EUR pairs)                    |
//| - Frankfurt: 07:00–09:00 UK (DAX/EUR pairs)                      |
//| - New York: 14:00–15:00 UK (US indices/USD pairs)                |
//| - Asian: 00:00–06:00 UK (breakout after Asian session)           |
//| - Pre-London: 02:00–07:00 UK (London breakout)                   |
//| - Custom: 23:00–08:00 for indices, 21:00–07:00 for gold          |
//| - Key: Best ranges are often the quiet session before a major open|
//| Your logic (23:00–18:00) is robust for indices and multi-session. |
//+------------------------------------------------------------------+

// Session selection
enum SessionType { SESSION_24H, SESSION_PEAK, SESSION_ASIAN };
input SessionType TradingSession = SESSION_24H; // Trading session
input int PeakStartHour = 9;    // Peak session start hour (local)
input int PeakEndHour = 17;     // Peak session end hour (local)
input int AsianStartHour = 1;   // Asian session start hour (local)
input int AsianEndHour = 8;     // Asian session end hour (local)

// Range time (editable)

// Global Variables
int BuyTicket1 = 0, BuyTicket2 = 0, SellTicket1 = 0, SellTicket2 = 0;
double RangeHigh = 0, RangeLow = 0;
datetime RangeStart, RangeEnd;
bool RangeIdentified = false;
string RangeBoxName = "";
string RangeTopLineName = "";
string RangeBottomLineName = "";
int EAInstanceID = 0;
int MagicBase = 10000;
double StartingBalance = 0;
double PerfWinRate = 0;
double PerfExpectancy = 0;
double PerfAvgWin = 0;
double PerfAvgLoss = 0;
int PerfMaxWinStreak = 0;
int PerfMaxLossStreak = 0;
double MaxDrawdown = 0;

// Add this line to declare LastBerlinDay globally
datetime LastBerlinDay = 0;

// Performance tracking
// ... (same as before, omitted here for brevity)

// Global Variables for Daily Trade Limits
int DailyTradesCount = 0;
datetime LastTradeDayReset = 0;
int DailyReversalTradesCount = 0;
bool BreakoutTradesPlacedToday = false; // NEW: Track if breakout trades have been placed today

//+------------------------------------------------------------------+
//| Risk tracking variables                                          |
//+------------------------------------------------------------------+

// ---- PROPER IMPLEMENTATIONS FOR MISSING FUNCTIONS ----
string StringTrim(string s) {
   while(StringLen(s) > 0 && (StringGetCharacter(s, 0) == ' ' || StringGetCharacter(s, 0) == '\t' || StringGetCharacter(s, 0) == '\n' || StringGetCharacter(s, 0) == '\r')) {
      s = StringSubstr(s, 1);
   }
   while(StringLen(s) > 0 && (StringGetCharacter(s, StringLen(s)-1) == ' ' || StringGetCharacter(s, StringLen(s)-1) == '\t' || StringGetCharacter(s, StringLen(s)-1) == '\n' || StringGetCharacter(s, StringLen(s)-1) == '\r')) {
      s = StringSubstr(s, 0, StringLen(s)-1);
   }
   return s;
}

string ErrorDescription(int code) {
   switch(code) {
      case 0: return "No error";
      case 1: return "No error returned";
      case 2: return "Common error";
      case 3: return "Invalid trade parameters";
      case 4: return "Trade server is busy";
      case 5: return "Old version of the client terminal";
      case 6: return "No connection with trade server";
      case 7: return "Not enough rights";
      case 8: return "Too frequent requests";
      case 9: return "Malfunctional trade operation";
      case 64: return "Account disabled";
      case 65: return "Invalid account";
      case 128: return "Trade timeout";
      case 129: return "Invalid price";
      case 130: return "Invalid stops";
      case 131: return "Invalid trade volume";
      case 132: return "Market is closed";
      case 133: return "Trade is disabled";
      case 134: return "Not enough money";
      case 135: return "Price changed";
      case 136: return "Off quotes";
      case 137: return "Broker is busy";
      case 138: return "Requote";
      case 139: return "Order is locked";
      case 140: return "Long positions only allowed";
      case 141: return "Too many requests";
      case 145: return "Modification denied because order too close to market";
      case 146: return "Trade context is busy";
      case 147: return "Expirations are denied by broker";
      case 148: return "Amount of open and pending orders has reached the limit";
      default: return "Unknown error " + IntegerToString(code);
   }
}
// -------------------------------------

double StartEquity = 0;
double DayStartEquity = 0;
double WeekStartEquity = 0;
bool DayLossLimitHit = false;
bool WeekLossLimitHit = false;
bool EmergencyStopHit = false;
int LastDay = -1;
int LastWeek = -1;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int AssetClass() {
   string sym = Symbol();

   // Manual uppercase conversion (StringToUpper doesn't exist in MQL4)
   string symUpper = "";
   for(int i = 0; i < StringLen(sym); i++) {
      ushort ch = (ushort)StringGetCharacter(sym, i);
      if(ch >= 97 && ch <= 122) ch = ch - 32; // Convert lowercase to uppercase
      string charStr = CharToString((char)ch);
      StringConcatenate(symUpper, symUpper, charStr);
   }

   // Check for indices, metals, and crypto symbols
   if(StringFind(symUpper, "NAS") >= 0 || StringFind(symUpper, "SPX") >= 0 || StringFind(symUpper, "DAX") >= 0 ||
      StringFind(symUpper, "DJ") >= 0 || StringFind(symUpper, "UK") >= 0 || StringFind(symUpper, "HK") >= 0 ||
      StringFind(symUpper, "BTC") >= 0 || StringFind(symUpper, "ETH") >= 0 || StringFind(symUpper, "LTC") >= 0 ||
      StringFind(symUpper, "XAU") >= 0 || StringFind(symUpper, "XAG") >= 0 || StringFind(symUpper, "GOLD") >= 0 || StringFind(symUpper, "SILVER") >= 0)
      return 1; // Indices/Metals/Crypto
   return 0; // Forex
}

// Unique EA instance ID and magic number base

double ActualLotSize1 = 0.01; // Effective lot size for Trade 1

int OnInit() {
    // --- Auto-sync ActualLotSize1 with LotSize if user changed LotSize but left LotSize1 at default ---
    if (MathAbs(LotSize1 - 0.01) < 1e-8 && MathAbs(LotSize - 0.01) > 1e-8) {
        ActualLotSize1 = LotSize;
        Print("[INFO] ActualLotSize1 auto-set to LotSize (" + DoubleToString(LotSize, 2) + ") because LotSize1 was left at default."); // No implicit conversion warning now
    } else {
        ActualLotSize1 = LotSize1;
    }

   // Assign unique EA instance ID and magic number base
   EAInstanceID = (int)ChartID();
   MagicBase = 10000 + (EAInstanceID % 90000);

   // Input validation
   string errMsg = "";
   if(LotSize <= 0) errMsg += "\nLot size must be positive.";
   if(InitialTradeRiskPercent < 0 || InitialTradeRiskPercent > 100) errMsg += "\nInitialTradeRiskPercent must be 0-100.";
   if(TradeTargetR < 0) errMsg += "\nTradeTargetR must be >= 0.";
   if(TradeTargetPoints < 0) errMsg += "\nTradeTargetPoints must be >= 0.";
   if(RiskAmount < 0) errMsg += "\nRiskAmount must be >= 0.";
   if(MaxDailyLoss < 0 || MaxDailyLoss > 100) errMsg += "\nMaxDailyLoss must be 0-100.";
   if(MaxWeeklyLoss < 0 || MaxWeeklyLoss > 100) errMsg += "\nMaxWeeklyLoss must be 0-100.";
   if(EmergencyStopEquityPercent < 1 || EmergencyStopEquityPercent > 100) errMsg += "\nEmergencyStopEquityPercent must be 1-100.";
   if(MinRangePoints < 0) errMsg += "\nMinRangePoints must be >= 0.";
   if(MaxRangePoints < 0) errMsg += "\nMaxRangePoints must be >= 0.";
   if(MaxSpreadPoints < 0) errMsg += "\nMaxSpreadPoints must be >= 0.";
   if(ADRDays < 1) errMsg += "\nADRDays must be >= 1.";
   if(MaxRangeADRMultiple < 0) errMsg += "\nMaxRangeADRMultiple must be >= 0.";
   if(AddedStopLoss < 0) errMsg += "\nAddedStopLoss must be >= 0.";
   if(errMsg != "") {
      Alert(StringFormat("EA initialization error: %s", errMsg));
      Print(StringFormat("EA initialization error: %s", errMsg));
      ExpertRemove();
      return(INIT_FAILED);
   }
   Print("[DEBUG] OnInit: Input RangeStartHour=", RangeStartHour, ", RangeStartMinute=", RangeStartMinute, ", RangeEndHour=", RangeEndHour, ", RangeEndMinute=", RangeEndMinute);
   // Risk tracking initialization
   StartEquity = AccountEquity();
   DayStartEquity = AccountEquity();
   WeekStartEquity = AccountEquity();
   DayLossLimitHit = false;
   WeekLossLimitHit = false;
   EmergencyStopHit = false;
   LastDay = TimeDayOfYear(TimeCurrent());
   LastWeek = TimeDayOfYear(TimeCurrent())/7;
   LastTradeDayReset = TimeCurrent(); // Initialize LastTradeDayReset
   DailyTradesCount = 0; // Initialize DailyTradesCount
   DailyReversalTradesCount = 0; // Initialize DailyReversalTradesCount
   // Auto-set BETriggerPoints/Percent based on asset class, unless user changed them
   static bool beSet = false;
   static int beTriggerPoints = 0;
   static double beTriggerPercent = 0;
   if(!beSet) {
      beTriggerPoints = BETriggerPoints;
      beTriggerPercent = BETriggerPercent;
      if(AssetClass() == 1) {
         if(beTriggerPoints == 0) beTriggerPoints = 3000;
         if(beTriggerPercent == 0) beTriggerPercent = 1.5;
      } else {
         if(beTriggerPoints == 0) beTriggerPoints = 300;
         if(beTriggerPercent == 0) beTriggerPercent = 1.5;
      }
      beSet = true;
   }
   EventSetTimer(60);
   ResetRangeTimes();
   ResetTradingHours();
   ResetFakeoutTradingHours(); // NEW: Initialize fakeout trading hours
   // ... Initialize performance tracking and counters
   UpdateInfoPanel();
   DrawRangeVisuals();
   return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
   DeleteRangeVisuals();
   EventKillTimer();
   Comment("");
}

//+------------------------------------------------------------------+
//| Risk limit logic                                                 |
//+------------------------------------------------------------------+
void UpdateRiskLimits() {
   datetime now = TimeCurrent();
   int day = TimeDayOfYear(now);
   int week = day/7;
   // New day/week resets
   if(day != LastDay) { DayStartEquity = AccountEquity(); DayLossLimitHit = false; LastDay = day; }
   if(week != LastWeek) { WeekStartEquity = AccountEquity(); WeekLossLimitHit = false; LastWeek = week; }
   double dayLoss = (DayStartEquity-AccountEquity())/DayStartEquity*100.0;
   double weekLoss = (WeekStartEquity-AccountEquity())/WeekStartEquity*100.0;
   double emergLoss = (AccountEquity()/StartEquity)*100.0;
   DayLossLimitHit = (dayLoss >= MaxDailyLoss);
   WeekLossLimitHit = (weekLoss >= MaxWeeklyLoss);
   EmergencyStopHit = (emergLoss <= EmergencyStopEquityPercent);
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer() {
   DrawRangeVisuals();
   datetime currentTime = TimeCurrent();
   int currentHour = TimeHour(currentTime);
   int currentMinute = TimeMinute(currentTime);
   // Cancel all orders at 10 PM (22:00)
   if(currentHour == 22 && currentMinute == 0) {
      Print("[DEBUG] 22:00 daily check: Managing end-of-day positions and orders.");
      ManageEndOfDayPositions();
      DeletePendingOrders();
   }
   // Also run session end logic
   if(IsTradingEnd(currentHour, currentMinute)) {
      Print("[DEBUG] End of trading session: Managing end-of-day positions and orders.");
      ManageEndOfDayPositions();
      DeletePendingOrders();
   }
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
   datetime currentTime = TimeCurrent();
   datetime berlinTime = ServerToBerlin(currentTime);
   MqlDateTime berlinDT;
   TimeToStruct(berlinTime, berlinDT);

   // --- 21:50 Berlin time: End-of-day logic (10 minutes before market freeze) ---
   static datetime lastEODBerlin = 0;
   if(berlinDT.hour == 21 && berlinDT.min == 50) {
        if(lastEODBerlin != BerlinMidnight(currentTime) + 21*3600 + 50*60) {
            Print("[EOD] 21:50 Berlin: Running end-of-day trade management (avoiding 22:00 market freeze).");
            ManageEndOfDayBerlin();
            lastEODBerlin = BerlinMidnight(currentTime) + 21*3600 + 50*60;
        }
        // Prevent further trading logic this tick
        return;
    }

   // --- STRATEGY TESTER SUPPORT: Force daily range creation ---
   static datetime lastTesterDay = 0;
   bool isTester = IsTesting();
   datetime today = StringToTime(TimeToString(currentTime, TIME_DATE));
   if (isTester && today != lastTesterDay) {
      // Simulate new day in tester: reset range and trading flags
      DayStartEquity = AccountEquity();
      DayLossLimitHit = false;
      LastBerlinDay = BerlinMidnight(currentTime);
      ResetRangeTimes();
      ResetTradingHours();
      RangeIdentified = false;
      DeleteRangeVisuals();
      DeletePendingOrders();
      FakeoutBuyOrderPlaced = false;
      FakeoutSellOrderPlaced = false;
      FakeoutLogicActive = false;
      DailyTradesCount = 0;
      DailyReversalTradesCount = 0;
      LastTradeDayReset = currentTime;
      lastTesterDay = today;
      BreakoutTradesPlacedToday = false; // NEW: Reset on new tester day
      Print("[DEBUG] [TESTER] New day detected. Range times reset. Tester date: ", TimeToString(today, TIME_DATE));
   }

   // --- DAILY RESET: If new Berlin day, reset range and flags ---
   datetime berlinDay = BerlinMidnight(currentTime);
   if(LastBerlinDay != berlinDay) {
      DayStartEquity = AccountEquity();
      DayLossLimitHit = false;
      LastBerlinDay = berlinDay;
      // Reset range and trading flags
      ResetRangeTimes();
      ResetTradingHours();
      RangeIdentified = false;
      DeleteRangeVisuals();
      DeletePendingOrders();
      FakeoutBuyOrderPlaced = false;
      FakeoutSellOrderPlaced = false;
      FakeoutLogicActive = false;
      DailyTradesCount = 0;
      DailyReversalTradesCount = 0;
      LastTradeDayReset = currentTime;
      BreakoutTradesPlacedToday = false; // NEW: Reset on new Berlin day
      Print("[DEBUG] OnTick: New Berlin day detected. Range times reset. Berlin date: ", TimeToString(berlinDay, TIME_DATE));
   }

   // --- CLEAR/HIDE RANGE BETWEEN 11:00-12:00 BERLIN TIME ---
   if(berlinDT.hour >= 11 && berlinDT.hour < 12) {
      if(RangeIdentified) {
         RangeIdentified = false;
         DeleteRangeVisuals();
         Print("[DEBUG] Berlin 11:00-12:00: Range cleared/hidden.");
      }
      // Prevent trading during this hour
      return;
   }

   // --- Only create range and allow trading if Berlin time is after 06:00 ---
   if(!RangeIdentified && berlinDT.hour >= 6) {
      Print("[DEBUG] Berlin >=6:00, attempting to identify range and place breakout trades. Berlin hour: ", berlinDT.hour);
      IdentifyRange();
      if(!ADRTooBig) {
         Print("[DEBUG] Attempting to place breakout trades at Berlin hour ", berlinDT.hour);
         if(!BreakoutTradesPlacedToday) { // NEW: Only place if not already placed today
            PlacePendingOrders();
            BreakoutTradesPlacedToday = true; // NEW: Mark as placed
            Print("[DEBUG] PlacePendingOrders() executed");
         } else {
            Print("[DEBUG] Breakout trades already placed today. Skipping new placements.");
         }
      } else {
         Print("Range too large compared to ADR, skipping trades for today.");
      }
      RangeIdentified = true;
   }

   // Enhanced Fakeout Logic - Check for fakeout opportunities
   CheckEnhancedFakeout();

   // Reversal Entry Logic
   if (EnableReversalEntryLogic && RangeIdentified) {
      if (MaxDailyReversalTrades > 0 && DailyReversalTradesCount >= MaxDailyReversalTrades) {
         Print("[REVERSAL LOGIC] Max daily reversal trades limit reached (", MaxDailyReversalTrades, "). Skipping new reversal trades.");
      } else {
         // Only attempt to place reversal trades if limit not reached
         double buffer = 0;
         if(EnableDynamicEntryBuffer) {
            double rangeSize = MathAbs(RangeHigh - RangeLow);
            if(EntryBufferPoints > 0) buffer = EntryBufferPoints * Point;
            else if(EntryBufferPercent > 0) buffer = rangeSize * EntryBufferPercent;
         } else {
            buffer = MarketInfo(Symbol(), MODE_SPREAD) * Point;
         }

         int openBuyTrades = 0;
         int openSellTrades = 0;
         // Count existing open trades by the EA
         for(int i = OrdersTotal() - 1; i >= 0; i--) {
            if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
               if(OrderSymbol() == Symbol() && OrderMagicNumber() >= MagicBase && OrderMagicNumber() < MagicBase + 90000) { // Check if it's this EA's trade
                  if(OrderType() == OP_BUY) openBuyTrades++;
                  else if(OrderType() == OP_SELL) openSellTrades++;
               }
            }
         }

         // Check for buy reversal entry
         if (openBuyTrades < MaxTrades && Ask < RangeHigh) {
            double buyStopLevel = RangeHigh + buffer;
            // Check if a pending buy stop order at this level already exists
            bool pendingBuyExists = false;
            for(int i = OrdersTotal() - 1; i >= 0; i--) {
               if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
                  if(OrderSymbol() == Symbol() && OrderMagicNumber() >= MagicBase && OrderMagicNumber() < MagicBase + 90000 && OrderType() == OP_BUYSTOP) {
                     if (MathAbs(OrderOpenPrice() - buyStopLevel) < Point) { // Check if price is close enough
                        pendingBuyExists = true;
                        break;
                     }
                  }
               }
            }
            if (!pendingBuyExists) {
               // Determine lot size, SL, TP for the new trade.
               // For simplicity, using settings of the first trade (Trade 1)
               double useLot = ActualLotSize1;
               double tpInput = TP1; int slInput = SL1; string commentInput = Comment1; color tradeColor = Trade1Color;
               double buySL = 0, buyTP = 0;
               // Calculate SL based on StopLossMethod or Trade 1 SL input
               if(slInput > 0) {
                  buySL = buyStopLevel - (slInput * Point);
               } else {
                  switch(StopLossMethod) {
                     case SL_RANGE:
                        buySL = RangeLow - (AddedStopLoss * Point);
                        break;
                     case SL_POINTS:
                        buySL = buyStopLevel - (FixedStopLossPoints * Point);
                        break;
                     case SL_PRICE:
                        buySL = FixedStopLossPrice;
                        break;
                     case SL_MIDRANGE:
                        {
                           double rangeMid = (RangeHigh + RangeLow) / 2.0;
                           buySL = rangeMid - (AddedStopLoss * Point);
                        }
                        break;
                  }
               }
               // Calculate TP based on Trade 1 TP input or default
               if(tpInput > 0) buyTP = buyStopLevel + tpInput*Point;
               else if(TradeTargetPoints > 0) buyTP = buyStopLevel + TradeTargetPoints*Point;
               else if(TradeTargetR > 0) buyTP = buyStopLevel + TradeTargetR*MathAbs(RangeHigh - RangeLow);

               int buyMagic = MagicBase + 2000 + openBuyTrades + 1; // Assign a unique magic number

               if (RobustOrderSend(Symbol(), OP_BUYSTOP, useLot, buyStopLevel, 3, buySL, buyTP, "Reversal Buy ("+commentInput+")", buyMagic, 0, tradeColor)) {
                  DailyReversalTradesCount++;
               }
            }
         }

         // Check for sell reversal entry
         if (openSellTrades < MaxTrades && Bid > RangeLow) {
            double sellStopLevel = RangeLow - buffer;
            // Check if a pending sell stop order at this level already exists
            bool pendingSellExists = false;
            for(int i = OrdersTotal() - 1; i >= 0; i--) {
               if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
                  if(OrderSymbol() == Symbol() && OrderMagicNumber() >= MagicBase && OrderMagicNumber() < MagicBase + 90000 && OrderType() == OP_SELLSTOP) {
                     if (MathAbs(OrderOpenPrice() - sellStopLevel) < Point) { // Check if price is close enough
                        pendingSellExists = true;
                        break;
                     }
                  }
               }
            }
            if (!pendingSellExists) {
               // Determine lot size, SL, TP for the new trade.
               // For simplicity, using settings of the first trade (Trade 1)
               double useLot = ActualLotSize1;
               double tpInput = TP1; int slInput = SL1; string commentInput = Comment1; color tradeColor = Trade1Color;
               double sellSL = 0, sellTP = 0;
               // Calculate SL based on StopLossMethod or Trade 1 SL input
               if(slInput > 0) {
                  sellSL = sellStopLevel + (slInput * Point);
               } else {
                  switch(StopLossMethod) {
                     case SL_RANGE:
                        sellSL = RangeHigh + (AddedStopLoss * Point);
                        break;
                     case SL_POINTS:
                        sellSL = sellStopLevel + (FixedStopLossPoints * Point);
                        break;
                     case SL_PRICE:
                        sellSL = FixedStopLossPrice;
                        break;
                     case SL_MIDRANGE:
                        {
                           double rangeMid = (RangeHigh + RangeLow) / 2.0;
                           sellSL = rangeMid + (AddedStopLoss * Point);
                        }
                        break;
                  }
               }
               // Calculate TP based on Trade 1 TP input or default
               if(tpInput > 0) sellTP = sellStopLevel - tpInput*Point;
               else if(TradeTargetPoints > 0) sellTP = sellStopLevel - TradeTargetPoints*Point;
               else if(TradeTargetR > 0) sellTP = sellStopLevel - TradeTargetR*MathAbs(RangeHigh - RangeLow);

               int sellMagic = MagicBase + 4000 + openSellTrades + 1; // Assign a unique magic number

               if (RobustOrderSend(Symbol(), OP_SELLSTOP, useLot, sellStopLevel, 3, sellSL, sellTP, "Reversal Sell ("+commentInput+")", sellMagic, 0, tradeColor)) {
                  DailyReversalTradesCount++;
               }
            }
         }
      }
   }

   ManagePositions();
}

//+------------------------------------------------------------------+
//| Helper: Reset Range Times                                        |
//+------------------------------------------------------------------+
void ResetRangeTimes() {
   datetime today = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
   // Use input parameters for range start and end times
   RangeStart = today + RangeStartHour * 3600 + RangeStartMinute * 60;
   RangeEnd = today + RangeEndHour * 3600 + RangeEndMinute * 60;

   // Handle cases where the end time is on the next day (e.g., 23:00 to 06:00)
   if (RangeEnd <= RangeStart) {
      RangeEnd = RangeEnd + 24 * 3600; // Add 24 hours to the end time
   }

   Print("[DEBUG] ResetRangeTimes: RangeStart set to ", TimeToString(RangeStart, TIME_DATE|TIME_MINUTES), ", RangeEnd set to ", TimeToString(RangeEnd, TIME_DATE|TIME_MINUTES), " using inputs ", RangeStartHour, ":", RangeStartMinute, " to ", RangeEndHour, ":", RangeEndMinute);
}

//+------------------------------------------------------------------+
//| Helper: Reset Trading Hours                                      |
//+------------------------------------------------------------------+
void ResetTradingHours() {
   if(!EnableCustomTradingHours) {
      TradingStart = 0;
      TradingEnd = 0;
      return;
   }

   datetime today = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
   TradingStart = today + CustomTradingStartHour * 3600 + CustomTradingStartMinute * 60;
   TradingEnd = today + CustomTradingEndHour * 3600 + CustomTradingEndMinute * 60;

   // Handle cases where the end time is on the next day
   if (TradingEnd <= TradingStart) {
      TradingEnd = TradingEnd + 24 * 3600; // Add 24 hours to the end time
   }

   Print("[DEBUG] ResetTradingHours: TradingStart set to ", TimeToString(TradingStart, TIME_DATE|TIME_MINUTES),
         ", TradingEnd set to ", TimeToString(TradingEnd, TIME_DATE|TIME_MINUTES),
         " using inputs ", CustomTradingStartHour, ":", CustomTradingStartMinute,
         " to ", CustomTradingEndHour, ":", CustomTradingEndMinute);
}

//+------------------------------------------------------------------+
//| Helper: Reset Fakeout Trading Hours                              |
//+------------------------------------------------------------------+
void ResetFakeoutTradingHours() {
  if(!EnableFakeoutCustomHours) {
     FakeoutTradingStart = 0; // Indicates 24/7 operation
     FakeoutTradingEnd = 0;   // Indicates 24/7 operation
     Print("[DEBUG] ResetFakeoutTradingHours: Fakeout trading set to 24/7 mode");
     return;
  }

  datetime today = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
  FakeoutTradingStart = today + FakeoutStartHour * 3600 + FakeoutStartMinute * 60;
  FakeoutTradingEnd = today + FakeoutEndHour * 3600 + FakeoutEndMinute * 60;

  // Handle cases where the end time is on the next day
  if (FakeoutTradingEnd <= FakeoutTradingStart) {
     FakeoutTradingEnd = FakeoutTradingEnd + 24 * 3600; // Add 24 hours to the end time
  }

  Print("[DEBUG] ResetFakeoutTradingHours: FakeoutTradingStart set to ", TimeToString(FakeoutTradingStart, TIME_DATE|TIME_MINUTES),
        ", FakeoutTradingEnd set to ", TimeToString(FakeoutTradingEnd, TIME_DATE|TIME_MINUTES),
        " using inputs ", FakeoutStartHour, ":", FakeoutStartMinute,
        " to ", FakeoutEndHour, ":", FakeoutEndMinute);
}

//+------------------------------------------------------------------+
//| Helper: Is within fakeout trading hours                          |
//+------------------------------------------------------------------+
bool IsWithinFakeoutTradingHours() {
  if(!EnableFakeoutCustomHours) {
     return true; // If custom fakeout hours are disabled, always allow fakeout logic
  }

  datetime currentTime = TimeCurrent();

  // Check if current time is within the custom fakeout trading hours
  if (FakeoutTradingEnd > FakeoutTradingStart) {
     // Same day trading session
     return (currentTime >= FakeoutTradingStart && currentTime <= FakeoutTradingEnd);
  } else {
     // Trading session crosses midnight
     return (currentTime >= FakeoutTradingStart || currentTime <= FakeoutTradingEnd);
  }
}

//+------------------------------------------------------------------+
//| Helper: Is within custom trading hours                           |
//+------------------------------------------------------------------+
bool IsWithinCustomTradingHours() {
   if(!EnableCustomTradingHours) {
      return true; // If custom hours are disabled, always allow trading
   }

   datetime currentTime = TimeCurrent();

   // Check if current time is within the custom trading hours
   if (TradingEnd > TradingStart) {
      // Same day trading session
      return (currentTime >= TradingStart && currentTime <= TradingEnd);
   } else {
      // Trading session crosses midnight
      return (currentTime >= TradingStart || currentTime <= TradingEnd);
   }
}

//+------------------------------------------------------------------+
//| Helper: Is within trading session                                |
//+------------------------------------------------------------------+
bool IsWithinTradingSession(int hour, int minute) {
   int startHour, endHour, startMinute = 0, endMinute = 59;
   switch(SessionPreset) {
      case 0: // 24hr
         startHour = 0; endHour = 23;
         break;
      case 1: // London (User specified 9:00-14:00)
         startHour = 9; endHour = 14;
         break;
      case 2: // New York (User specified 15:00-18:00)
         startHour = 15; endHour = 18;
         break;
      case 3: // Tokyo (Keeping existing preset 1:00-9:00)
         startHour = 1; endHour = 9;
         break;
      case 4: // Frankfurt (Keeping existing preset 9:00-17:00)
         startHour = 9; endHour = 17;
         break;
      case 5: // Asian (User specified 00:00-06:00)
         startHour = 0; endHour = 6;
         break;
      case 6: // Custom
         startHour = CustomSessionStartHour; endHour = CustomSessionEndHour;
         break;
      default: // Default to 24hr if preset is invalid
         startHour = 0; endHour = 23;
         break;
   }

   if (startHour == endHour) { // Handles sessions that cross midnight or are exactly one hour
       if (startHour == 23 && endHour == 23) return (hour == 23); // 23:00 to 23:59
       if (startHour == 0 && endHour == 0) return (hour == 0); // 00:00 to 00:59
       // For single hour sessions not crossing midnight, standard check works
   }

   if (startHour < endHour) { // Session does not cross midnight
      return (hour > startHour || (hour == startHour && minute >= startMinute)) &&
             (hour < endHour || (hour == endHour && minute <= endMinute));
   } else { // Session crosses midnight (e.g., 22:00 to 04:00)
      return (hour > startHour || (hour == startHour && minute >= startMinute)) ||
             (hour < endHour || (hour == endHour && minute <= endMinute));
   }
}

//+------------------------------------------------------------------+
//| Helper: Is Trading End                                           |
//+------------------------------------------------------------------+
bool IsTradingEnd(int hour, int minute) {
   int endHour, endMinute = 0; // Assuming session ends on the hour for simplicity based on user input

   switch(SessionPreset) {
      case 0: // 24hr (End of range period logic used here)
         endHour = RangeEndHour; endMinute = RangeEndMinute;
         break;
      case 1: // London (User specified 9:00-14:00, end at 14:00)
         endHour = 14; endMinute = 0;
         break;
      case 2: // New York (User specified 15:00-18:00, end at 18:00)
         endHour = 18; endMinute = 0;
         break;
      case 3: // Tokyo (Keeping existing preset 1:00-9:00, end at 9:00)
         endHour = 9; endMinute = 0;
         break;
      case 4: // Frankfurt (Keeping existing preset 9:00-17:00, end at 17:00)
         endHour = 17; endMinute = 0;
         break;
      case 5: // Asian (User specified 00:00-06:00, end at 6:00)
         endHour = 6; endMinute = 0;
         break;
      case 6: // Custom
         endHour = CustomSessionEndHour; endMinute = 0; // Assuming custom ends on the hour
         break;
      default: // Default to end of range period
         endHour = RangeEndHour; endMinute = RangeEndMinute;
         break;
   }
   return (hour == endHour && minute == endMinute);
}

//+------------------------------------------------------------------+
//| Function to identify the price range                             |
//+------------------------------------------------------------------+
void IdentifyRange() {
   RangeHigh = -DBL_MAX;
   RangeLow = DBL_MAX;
   int barsInRange = 0;

   Print("[DEBUG] IdentifyRange: Searching for range between ", TimeToString(RangeStart, TIME_DATE|TIME_MINUTES),
         " and ", TimeToString(RangeEnd, TIME_DATE|TIME_MINUTES));

   for(int i = 0; i < Bars; i++) {
      datetime barTime = Time[i];
      if(barTime >= RangeStart && barTime <= RangeEnd) {
         if(High[i] > RangeHigh) RangeHigh = High[i];
         if(Low[i] < RangeLow) RangeLow = Low[i];
         barsInRange++;
      } else if(barTime < RangeStart) {
         break;
      }
   }

   // Validate range
   if(barsInRange == 0) {
      Print("[WARNING] IdentifyRange: No bars found in range period. Check range times.");
      RangeHigh = 0;
      RangeLow = 0;
      return;
   }

   if(RangeHigh == -DBL_MAX || RangeLow == DBL_MAX) {
      Print("[WARNING] IdentifyRange: Invalid range data. RangeHigh=", RangeHigh, ", RangeLow=", RangeLow);
      RangeHigh = 0;
      RangeLow = 0;
      return;
   }

   Print("[DEBUG] IdentifyRange: Found ", barsInRange, " bars in range period");
   // Calculate ADR (Average Daily Range)
   double adrSum = 0;
   int adrCount = 0;
   if (ADRType == 0) {
      // Daily ADR (default)
      for(int d = 1; d <= ADRDays; d++) {
         double dayHigh = -DBL_MAX, dayLow = DBL_MAX;
         datetime dayStart = RangeStart - d*86400;
         datetime dayEnd = RangeEnd - d*86400;
         for(int iBar = 0; iBar < Bars; iBar++) {
            datetime barTime = Time[iBar];
            if(barTime >= dayStart && barTime <= dayEnd) {
               if(High[iBar] > dayHigh) dayHigh = High[iBar];
               if(Low[iBar] < dayLow) dayLow = Low[iBar];
            } else if(barTime < dayStart) {
               break;
            }
         }
         if(dayHigh > -DBL_MAX && dayLow < DBL_MAX) {
            adrSum += MathAbs(dayHigh - dayLow);
            adrCount++;
         }
      }
      CurrentADR = (adrCount > 0) ? adrSum / adrCount : 0;
   } else {
      // Weekly ADR: average weekly range / 5
      int weeks = MathMax(1, ADRDays/5);
      for(int w = 1; w <= weeks; w++) {
         double weekHigh = -DBL_MAX, weekLow = DBL_MAX;
         datetime weekStart = RangeStart - w*7*86400;
         datetime weekEnd = RangeEnd - (w-1)*7*86400;
         for(int iBar = 0; iBar < Bars; iBar++) {
            datetime barTime = Time[iBar];
            if(barTime >= weekStart && barTime <= weekEnd) {
               if(High[iBar] > weekHigh) weekHigh = High[iBar];
               if(Low[iBar] < weekLow) weekLow = Low[iBar];
            } else if(barTime < weekStart) {
               break;
            }
         }
         if(weekHigh > -DBL_MAX && weekLow < DBL_MAX) {
            adrSum += MathAbs(weekHigh - weekLow) / 5.0;
            adrCount++;
         }
      }
      CurrentADR = (adrCount > 0) ? adrSum / adrCount : 0;
   }
   double todayRange = MathAbs(RangeHigh - RangeLow);
   ADRTooBig = (UseADRLimit && CurrentADR > 0 && todayRange > MaxRangeADRMultiple * CurrentADR);
   DrawRangeVisuals();
   Print("Range identified: High = "+DoubleToString(RangeHigh,2)+", Low = "+DoubleToString(RangeLow,2)+". ADR="+DoubleToString(CurrentADR,2)+". ADRTooBig="+(ADRTooBig?"true":"false"));
}

//+------------------------------------------------------------------+
//| Draw range box and lines                                         |
//+------------------------------------------------------------------+
void DrawRangeVisuals() {
   DeleteRangeVisuals();
   color boxColors[7] = {clrAqua, clrOrange, clrViolet, clrLightSeaGreen, clrYellow, clrPink, clrGray}; // Mon-Sun
   color topColors[7] = {clrBlue, clrRed, clrMagenta, clrGreen, clrRed, clrPurple, clrSlateGray};
   color bottomColors[7] = {clrBlue, clrRed, clrMagenta, clrGreen, clrLime, clrPurple, clrSlateGray};
   datetime today = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
   int todayDOW = TimeDayOfWeek(today); // 1=Mon, 5=Fri
   int startDOW = (todayDOW == 0) ? 1 : 1; // Always start from Monday
   // Only draw today's range box (midnight to 22:00)
   string dayStr = TimeToString(today, TIME_DATE);
   datetime rngStart = today;
   datetime rngEnd = today + 22*3600; // 00:00 to 22:00 (for drawing)
   // Calculate hi/lo using user-defined range times
   datetime rangeHiLoStart = today + RangeStartHour * 3600 + RangeStartMinute * 60;
   datetime rangeHiLoEnd = today + RangeEndHour * 3600 + RangeEndMinute * 60;

   // Handle cases where the end time is on the next day (e.g., 23:00 to 06:00)
   if (rangeHiLoEnd <= rangeHiLoStart) {
      rangeHiLoEnd = rangeHiLoEnd + 24 * 3600; // Add 24 hours to the end time
   }
   double hi = -DBL_MAX, lo = DBL_MAX;
   for(int i=0; i<Bars; i++) {
      datetime barTime = Time[i];
      if(barTime >= rangeHiLoStart && barTime <= rangeHiLoEnd) {
         if(High[i] > hi) hi = High[i];
         if(Low[i] < lo) lo = Low[i];
      } else if(barTime < rangeHiLoStart) {
         break;
      }
   }
   if(hi == -DBL_MAX || lo == DBL_MAX) return; // No data
   int colorIdx = (todayDOW-1)%7;
   color boxCol = boxColors[colorIdx];
   color topCol = topColors[colorIdx];
   color botCol = bottomColors[colorIdx];
   string boxName = "RangeBox_" + dayStr;
   string topName = "RangeTop_" + dayStr;
   string botName = "RangeBottom_" + dayStr;
   // Draw rectangle from 00:00 to 22:00, at hi/lo (no trendlines)
   ObjectCreate(0, boxName, OBJ_RECTANGLE, 0, rngStart, hi, rngEnd, lo);
   ObjectSetInteger(0, boxName, OBJPROP_COLOR, boxCol); // Border color
   ObjectSetInteger(0, boxName, OBJPROP_BACK, true);
   ObjectSetInteger(0, boxName, OBJPROP_WIDTH, 2); // Thicker border for visibility
   ObjectSetInteger(0, boxName, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, boxName, OBJPROP_FILL, true);
}


//+------------------------------------------------------------------+
//| Delete range visuals                                             |
//+------------------------------------------------------------------+
void DeleteRangeVisuals() {
   ObjectsDeleteAll(0, "RangeBox_");
   ObjectsDeleteAll(0, "RangeTop_");
   ObjectsDeleteAll(0, "RangeBottom_");
}

//+------------------------------------------------------------------+
//| Place pending orders                                             |
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| FAKEOUT REVERSAL (Fade) LOGIC                                   |
//+------------------------------------------------------------------+
bool fakeoutUp = false, fakeoutDown = false;
bool fakeoutChecked = false;

void CheckFakeoutBeforeSession() {
   if(!EnableFakeoutFade || fakeoutChecked) return;
   datetime sessionOpen = StrToTime(TimeToStr(TimeCurrent(), TIME_DATE) + " " + IntegerToString(SessionOpenHour,2,'0') + ":" + IntegerToString(SessionOpenMinute,2,'0'));
   if(TimeCurrent() < sessionOpen) {
      double lastHigh = RangeHigh;
      double lastLow = RangeLow;
      double price = iClose(Symbol(), 0, 0);
      if(price > lastHigh) fakeoutUp = true;
      if(price < lastLow) fakeoutDown = true;
   }
   if(TimeCurrent() >= sessionOpen) fakeoutChecked = true;
}

//+------------------------------------------------------------------+
//| Enhanced Fakeout Logic                                           |
//+------------------------------------------------------------------+
void CheckEnhancedFakeout() {
   if(!EnableEnhancedFakeout || !RangeIdentified) {
      FakeoutLogicActive = false;
      return;
   }

   // Check if we're within the defined fakeout trading hours (or if custom hours are disabled for 24/7 operation)
   bool withinFakeoutHours = IsWithinFakeoutTradingHours();

   if(!withinFakeoutHours) {
      FakeoutLogicActive = false;
      return;
   }

   FakeoutLogicActive = true;

   double currentPrice = iClose(Symbol(), 0, 0);
   double buffer = MarketInfo(Symbol(), MODE_SPREAD) * Point;

   // Enhanced fakeout logic: Place SELL orders when price breaks above range high
   if(currentPrice > RangeHigh && !FakeoutSellOrderPlaced) {
      // Calculate lot size using the same logic as regular trades
      double useLot = ActualLotSize1;
      if(useLot <= 0) {
         double stopDist = MathAbs(RangeHigh - RangeLow) / Point;
         if(InitialTradeRiskPercent > 0 && stopDist > 0 && AccountBalance() > 0)
            useLot = CalcInitialLot(stopDist);
         else
            useLot = MarketInfo(Symbol(), MODE_MINLOT);
      }

      // Calculate SL and TP for fakeout sell order
      double sellPrice = RangeHigh;
      double sellSL = 0, sellTP = 0;

      // Use same SL calculation as regular trades
      switch(StopLossMethod) {
         case SL_RANGE:
            sellSL = RangeHigh + (AddedStopLoss * Point);
            break;
         case SL_POINTS:
            sellSL = sellPrice + (FixedStopLossPoints * Point);
            break;
         case SL_PRICE:
            sellSL = FixedStopLossPrice;
            break;
         case SL_MIDRANGE:
            {
               double rangeMid = (RangeHigh + RangeLow) / 2.0;
               sellSL = rangeMid + (AddedStopLoss * Point);
            }
            break;
      }

      // Calculate TP
      if(TradeTargetPoints > 0) {
         sellTP = sellPrice - TradeTargetPoints * Point;
      } else if(TradeTargetR > 0) {
         double rangeSize = MathAbs(RangeHigh - RangeLow);
         sellTP = sellPrice - TradeTargetR * rangeSize;
      }

      // Check if fakeout sell order doesn't already exist
      int fakeoutSellMagic = MagicBase + 6000; // Unique magic for fakeout sells
      bool fakeoutSellExists = false;
      for(int i = 0; i < OrdersTotal(); i++) {
         if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == fakeoutSellMagic) {
               fakeoutSellExists = true;
               break;
            }
         }
      }

      if(!fakeoutSellExists) {
         if(RobustOrderSend(Symbol(), OP_SELL, useLot, Bid, 3, sellSL, sellTP, "Enhanced Fakeout Sell", fakeoutSellMagic, 0, clrRed)) {
            FakeoutSellOrderPlaced = true;
            Print("[ENHANCED FAKEOUT] Sell order placed at range high breakout. Price: ", DoubleToString(currentPrice, Digits));
         }
      }
   }

   // Enhanced fakeout logic: Place BUY orders when price breaks below range low
   if(currentPrice < RangeLow && !FakeoutBuyOrderPlaced) {
      // Calculate lot size using the same logic as regular trades
      double useLot = ActualLotSize1;
      if(useLot <= 0) {
         double stopDist = MathAbs(RangeHigh - RangeLow) / Point;
         if(InitialTradeRiskPercent > 0 && stopDist > 0 && AccountBalance() > 0)
            useLot = CalcInitialLot(stopDist);
         else
            useLot = MarketInfo(Symbol(), MODE_MINLOT);
      }

      // Calculate SL and TP for fakeout buy order
      double buyPrice = RangeLow;
      double buySL = 0, buyTP = 0;

      // Use same SL calculation as regular trades
      switch(StopLossMethod) {
         case SL_RANGE:
            buySL = RangeLow - (AddedStopLoss * Point);
            break;
         case SL_POINTS:
            buySL = buyPrice - (FixedStopLossPoints * Point);
            break;
         case SL_PRICE:
            buySL = FixedStopLossPrice;
            break;
         case SL_MIDRANGE:
            {
               double rangeMid = (RangeHigh + RangeLow) / 2.0;
               buySL = rangeMid - (AddedStopLoss * Point);
            }
            break;
      }

      // Calculate TP
      if(TradeTargetPoints > 0) {
         buyTP = buyPrice + TradeTargetPoints * Point;
      } else if(TradeTargetR > 0) {
         double rangeSize = MathAbs(RangeHigh - RangeLow);
         buyTP = buyPrice + TradeTargetR * rangeSize;
      }

      // Check if fakeout buy order doesn't already exist
      int fakeoutBuyMagic = MagicBase + 5000; // Unique magic for fakeout buys
      bool fakeoutBuyExists = false;
      for(int i = 0; i < OrdersTotal(); i++) {
         if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == fakeoutBuyMagic) {
               fakeoutBuyExists = true;
               break;
            }
         }
      }

      if(!fakeoutBuyExists) {
         if (RobustOrderSend(Symbol(), OP_BUY, useLot, Ask, 3, buySL, buyTP, "Enhanced Fakeout Buy", fakeoutBuyMagic, 0, clrGreen)) {
            FakeoutBuyOrderPlaced = true;
            Print("[ENHANCED FAKEOUT] Buy order placed at range low breakout. Price: ", DoubleToString(currentPrice, Digits));
         }
      }
   }
}

bool OpenPositionExists(int type, int magic) {
    for(int i=0; i<OrdersTotal(); i++) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && OrderSymbol()==Symbol() && OrderMagicNumber()==magic && (OrderType()==OP_BUY||OrderType()==OP_SELL)) {
            if((type==OP_BUYSTOP && OrderType()==OP_BUY) || (type==OP_SELLSTOP && OrderType()==OP_SELL)) return true;
        }
    }
    return false;
}

bool PendingOrderExists(int type, int magic, double price, double sl, double tp, double lot, double priceTol=0) {
    if(priceTol == 0) priceTol = 10*Point;
    for(int i=0; i<OrdersTotal(); i++) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && OrderSymbol()==Symbol() && OrderMagicNumber()==magic && OrderType()==type) {
            if(MathAbs(OrderOpenPrice()-price)<=priceTol && MathAbs(OrderLots()-lot)<1e-6 && MathAbs(OrderStopLoss()-sl)<priceTol && MathAbs(OrderTakeProfit()-tp)<priceTol)
                return true;
        }
    }
    return false;
}

void PlacePendingOrders() {
   // Check if we should respect custom trading hours for regular pending orders
   if(EnableCustomTradingHours && !IsWithinCustomTradingHours()) {
      Print("[DEBUG] Skipping trade placement: outside custom trading hours");
      return;
   }

   // NEW: Prevent placing more than one set of breakout trades per day
   if(BreakoutTradesPlacedToday) {
      Print("[DEBUG] Breakout trades already placed today. Skipping PlacePendingOrders().");
      return;
   }

   double minLot = MarketInfo(Symbol(), MODE_MINLOT);
   double lot = AlwaysUseMinLot ? minLot : MathMax(LotSize, minLot);
   double spread = MarketInfo(Symbol(), MODE_SPREAD) * Point;
   double atr = 0;
   if(EnableATRFilter) atr = iATR(Symbol(), 0, ATRPeriod, 0);

   // Range size filters
   double rangeSize = MathAbs(RangeHigh - RangeLow);
   double rangeSizePoints = rangeSize / Point;
   bool validRange = true;

   // ATR/volatility-based range filter
   if(EnableATRFilter && atr > 0) {
      double minRange = MinRangeATR * atr;
      double maxRange = MaxRangeATR * atr;
      if(rangeSize < minRange || rangeSize > maxRange) {
         Print("[DEBUG] Skipping trade placement: range size ", DoubleToString(rangeSize,2), " outside ATR bounds (", DoubleToString(minRange,2), "-", DoubleToString(maxRange,2), ")");
         validRange = false;
      }
   }

   // Dynamic range filter (Min/Max range points)
   if(MinRangePoints > 0 && rangeSizePoints < MinRangePoints) {
      Print("[DEBUG] Skipping trade placement: range size ", DoubleToString(rangeSizePoints,1), " points below minimum (", DoubleToString(MinRangePoints,1), " points)");
      validRange = false;
   }
   if(MaxRangePoints > 0 && rangeSizePoints > MaxRangePoints) {
      Print("[DEBUG] Skipping trade placement: range size ", DoubleToString(rangeSizePoints,1), " points above maximum (", DoubleToString(MaxRangePoints,1), " points)");
      validRange = false;
   }

   if(!validRange) {
      Print("[DEBUG] Skipping trade placement: invalid range");
      return;
   }

   // Dynamic entry buffer
   double buffer = 0;
   if(EnableDynamicEntryBuffer) {
      if(EntryBufferPoints > 0) buffer = EntryBufferPoints * Point;
      else if(EntryBufferPercent > 0) buffer = rangeSize * EntryBufferPercent;
   } else {
      buffer = spread;
   }
   double buyStop = RangeHigh + buffer;
   double sellStop = RangeLow - buffer;

   // --- Fakeout Reversal Logic ---
   datetime sessionOpen = StrToTime(TimeToStr(TimeCurrent(), TIME_DATE) + " " + IntegerToString(SessionOpenHour,2,'0') + ":" + IntegerToString(SessionOpenMinute,2,'0'));
   bool fadeSell = false, fadeBuy = false;
   if(EnableFakeoutFade && TimeCurrent() >= sessionOpen) {
      if(fakeoutUp) fadeSell = true;
      if(fakeoutDown) fadeBuy = true;
   }

   // --- ONLY PLACE ORDERS IN THE DIRECTION THAT HAS NOT ALREADY BROKEN OUT ---
   double lastPrice = iClose(Symbol(), 0, 0);
   bool allowBuy = false;
   bool allowSell = false;
   if(!EnableReversalEntryLogic) {
      // If price is inside the range, allow both
      if(lastPrice <= RangeHigh && lastPrice >= RangeLow) {
         allowBuy = true;
         allowSell = true;
      } else if(lastPrice > RangeHigh) {
         // Price already broke out to the upside, only allow sell
         allowSell = true;
      } else if(lastPrice < RangeLow) {
         // Price already broke out to the downside, only allow buy
         allowBuy = true;
      }
   } else {
      // In reversal mode, allow both
      allowBuy = true;
      allowSell = true;
   }

   for(int t=1; t<=NumBreakoutTrades; t++) {
      // Per-trade settings
      // User-selected lot size is always prioritized. Only fallback to risk-based or min lot if LotSize1 is not set or invalid.
      double useLot = (t == 1) ? ActualLotSize1 : LotSize1;
      if(t == 2) useLot = LotSize2;
      else if(t >= 3) useLot = LotSize3;
      if(useLot <= 0) {
         // Fallback: use risk-based or broker minimum if user input is not valid
         double stopDist = MathAbs(RangeHigh - RangeLow) / Point;
         if(InitialTradeRiskPercent > 0 && stopDist > 0 && AccountBalance() > 0)
            useLot = CalcInitialLot(stopDist);
         else if(AlwaysUseMinLot)
            useLot = MarketInfo(Symbol(), MODE_MINLOT);
         else
            useLot = 0.01; // Final fallback
      }
      double tpInput = TP1; int slInput = SL1, ruleInput = Trade1Rule; string commentInput = Comment1; color tradeColor = Trade1Color;
      if(t == 2) { tpInput = TP2; slInput = SL2; ruleInput = Trade2Rule; commentInput = Comment2; tradeColor = Trade2Color; }
      else if(t >= 3) { tpInput = TP3; slInput = SL3; ruleInput = Trade3Rule; commentInput = Comment3; tradeColor = Trade3Color; }
      TradeRuleType rule = (ruleInput == 1) ? RuleTightSL : (ruleInput == 2) ? RuleBreakevenRunner : RuleNormal;
      double buySL = 0, sellSL = 0;
      string buyComment = commentInput, sellComment = commentInput;
      int buyMagic = MagicBase + 2000 + t, sellMagic = MagicBase + 4000 + t;
      color buyColor = tradeColor, sellColor = tradeColor;
      double staggeredOffset = 0;
      if(EnableStaggeredOrders && t > 1) staggeredOffset = rangeSize * StaggeredOrderPercent * (t-1);
      double buyStopLevel = buyStop + staggeredOffset;
      double sellStopLevel = sellStop - staggeredOffset;

      // Calculate Stop Loss based on selected method
      double calculatedBuySL = 0;
      double calculatedSellSL = 0;

      switch(StopLossMethod) {
          case SL_RANGE:
              calculatedBuySL = RangeLow - (AddedStopLoss * Point);
              calculatedSellSL = RangeHigh + (AddedStopLoss * Point);
              break;
          case SL_POINTS:
              calculatedBuySL = buyStopLevel - (FixedStopLossPoints * Point);
              calculatedSellSL = sellStopLevel + (FixedStopLossPoints * Point);
              break;
          case SL_PRICE:
              calculatedBuySL = FixedStopLossPrice;
              calculatedSellSL = FixedStopLossPrice;
              break;
          case SL_MIDRANGE:
              {
                 double rangeMid = (RangeHigh + RangeLow) / 2.0;
                 calculatedBuySL = rangeMid - (AddedStopLoss * Point);
                 calculatedSellSL = rangeMid + (AddedStopLoss * Point);
              }
              break;
      }

      // Apply per-trade SL offset if slInput > 0 (overrides method calculation for this trade)
      if (slInput > 0) {
          calculatedBuySL = buyStopLevel - (slInput * Point);
          calculatedSellSL = sellStopLevel + (slInput * Point);
      }

      double buyTP = 0, sellTP = 0;
      if(tpInput > 0) {
         buyTP = buyStopLevel + tpInput*Point;
         sellTP = sellStopLevel - tpInput*Point;
      } else if(TradeTargetPoints > 0) {
         buyTP = buyStopLevel + TradeTargetPoints*Point;
         sellTP = sellStopLevel - TradeTargetPoints*Point;
      } else if(TradeTargetR > 0) {
         buyTP = buyStopLevel + TradeTargetR*rangeSize;
         sellTP = sellStopLevel - TradeTargetR*rangeSize;
      }
      // Check for existing open buy position for this slot
      if(OpenPositionExists(OP_BUYSTOP, buyMagic)) {
          Print("[DEBUG] Open BUY position exists for slot ", IntegerToString(t), ". Not placing new pending buy order.");
      } else if(!PendingOrderExists(OP_BUYSTOP, buyMagic, buyStopLevel, calculatedBuySL, buyTP, useLot)) {
          Print("[DEBUG] Sending BUY STOP order for slot ", IntegerToString(t), " at price ", DoubleToString(buyStopLevel,Digits));
          int ticket = OrderSend(Symbol(), OP_BUYSTOP, useLot, buyStopLevel, 3, calculatedBuySL, buyTP, "Normal Breakout Buy "+IntegerToString(t)+" ("+buyComment+")", buyMagic, 0, buyColor);
          if(ticket < 0) {
              int err = GetLastError();
              Print("[ERROR] Failed to place BUY STOP order. Error code: ", err);
          } else {
              Print("[DEBUG] BUY STOP order placed. Ticket: ", ticket);
          }
      } else {
          Print("[DEBUG] Buy Stop order already exists for slot ", IntegerToString(t), ". No duplicate placed.");
      }
      // Check for existing open sell position for this slot
      if(OpenPositionExists(OP_SELLSTOP, sellMagic)) {
          Print("[DEBUG] Open SELL position exists for slot ", IntegerToString(t), ". Not placing new pending sell order.");
      } else if(!PendingOrderExists(OP_SELLSTOP, sellMagic, sellStopLevel, calculatedSellSL, sellTP, useLot)) {
          Print("[DEBUG] Sending SELL STOP order for slot ", IntegerToString(t), " at price ", DoubleToString(sellStopLevel,Digits));
          int ticket = OrderSend(Symbol(), OP_SELLSTOP, useLot, sellStopLevel, 3, calculatedSellSL, sellTP, "Normal Breakout Sell "+IntegerToString(t)+" ("+sellComment+")", sellMagic, 0, sellColor);
          if(ticket < 0) {
              int err = GetLastError();
              Print("[ERROR] Failed to place SELL STOP order. Error code: ", err);
          } else {
              Print("[DEBUG] SELL STOP order placed. Ticket: ", ticket);
          }
      } else {
          Print("[DEBUG] Sell Stop order already exists for slot ", IntegerToString(t), ". No duplicate placed.");
      }
   }
}

// --- Fix GetBerlinOffset DST logic ---
int GetBerlinOffset(datetime t) {
   // EMERGENCY FIX: Your broker server time appears to be different
   // If trades are placing at 04:35 local time instead of 06:00,
   // the server might be UTC+2 or UTC+3

   // Let's try different offsets to match 06:00 Berlin time
   // If local 04:35 should be 06:00 Berlin, then offset = +1.5 hours
   // But since we can't do half hours, let's try UTC+2

   return 2 * 3600; // Try UTC+2 to fix timing

   /* ORIGINAL LOGIC - DISABLED FOR DEBUGGING
   // Berlin is UTC+1 (CET) - Standard Time (Winter)
   // Currently Germany is observing Standard Time (CET = UTC+1)
   return 1 * 3600; // CET (UTC+1) - Standard Time
   */
}

// Converts server time to Berlin time
datetime ServerToBerlin(datetime t) {
   return t + GetBerlinOffset(t);
}

// Returns Berlin date (midnight) for a given server time
datetime BerlinMidnight(datetime t) {
   datetime berlin = ServerToBerlin(t);
   MqlDateTime dt;
   TimeToStruct(berlin, dt);
   return StringToTime(StringFormat("%04d.%02d.%02d 00:00", dt.year, dt.mon, dt.day));
}

// --- Add stubs for missing functions so the file compiles ---
void UpdateInfoPanel() { /* ...implement as needed... */ }
void DeletePendingOrders() { /* ...implement as needed... */ }
bool RobustOrderSend(string symbol, int op, double lot, double price, int slippage, double sl, double tp, string comment, int magic, int expiry, color arrow_color) { return false; }
void ManageEndOfDayPositions() { /* ...implement as needed... */ }

// --- Add this helper function to manage end-of-day logic at 21:50 Berlin time ---
void ManageEndOfDayBerlin() {
    int closedTrades = 0, deletedPending = 0;
    for(int i = OrdersTotal() - 1; i >= 0; i--) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() >= MagicBase && OrderMagicNumber() < MagicBase + 90000) {
                int type = OrderType();
                int ticket = OrderTicket();

                // --- Close ALL open trades (winning and losing) ---
                if(type == OP_BUY || type == OP_SELL) {
                    bool closed = OrderClose(ticket, OrderLots(), (type == OP_BUY ? Bid : Ask), 3, clrRed);
                    if(closed) {
                        closedTrades++;
                        Print("[EOD] Closed trade ticket ", ticket, " (", (type == OP_BUY ? "BUY" : "SELL"), ")");
                    } else {
                        Print("[EOD] Failed to close trade ticket ", ticket, " Error: ", GetLastError());
                    }
                }
                // --- Delete ALL pending orders ---
                if(type == OP_BUYSTOP || type == OP_SELLSTOP || type == OP_BUYLIMIT || type == OP_SELLLIMIT) {
                    bool deleted = OrderDelete(ticket);
                    if(deleted) {
                        deletedPending++;
                        Print("[EOD] Deleted pending order ticket ", ticket);
                    } else {
                        Print("[EOD] Failed to delete pending order ticket ", ticket, " Error: ", GetLastError());
                    }
                }
            }
        }
    }
    // Reset the breakout trades flag so new trades can be placed next day
    BreakoutTradesPlacedToday = false;
    Print("[EOD] End-of-day Berlin 22:00: Closed ", closedTrades, " trades (all), deleted ", deletedPending, " pending orders. Reset BreakoutTradesPlacedToday flag.");
}

// --- Restore ManagePositions function ---
void ManagePositions() {
    for(int i = OrdersTotal() - 1; i >= 0; i--) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() >= MagicBase && OrderMagicNumber() < MagicBase + 90000) {
                int type = OrderType();
                double openPrice = OrderOpenPrice();
                double lots = OrderLots();
                int ticket = OrderTicket();
                double profit = OrderProfit();
                double curSL = OrderStopLoss();
                double curTP = OrderTakeProfit();

                // --- Breakeven Logic ---
                if(UseBreakEvenProfitAmount && BreakEvenProfitAmount > 0) {
                    if(profit >= BreakEvenProfitAmount && (type == OP_BUY || type == OP_SELL)) {
                        // Only move SL to BE if not already at BE
                        if((type == OP_BUY && (curSL < openPrice || curSL == 0)) ||
                           (type == OP_SELL && (curSL > openPrice || curSL == 0))) {
                            bool mod = OrderModify(ticket, openPrice, openPrice, curTP, 0, clrAqua);
                            if(mod)
                                Print("[INFO] Breakeven (profit): SL moved to BE. Ticket ", ticket);
                            else
                                Print("[ERROR] Breakeven (profit): Failed to move SL to BE. Ticket ", ticket, " Error: ", GetLastError());
                        }
                    }
                } else if(UseBreakEven && (type == OP_BUY || type == OP_SELL)) {
                    double beTrigger = 0;
                    if(UseBreakEvenPercent && BreakEvenPercent >  0) {
                        beTrigger = openPrice * (BreakEvenPercent / 100.0);
                    } else if(BreakEvenPoints > 0) {
                        beTrigger = BreakEvenPoints * Point;
                    } else if(BETriggerPoints > 0) {
                        beTrigger = BETriggerPoints * Point;
                    }
                    // For BUY
                    if(type == OP_BUY && Bid - openPrice >= beTrigger && (curSL < openPrice || curSL == 0)) {
                        bool mod = OrderModify(ticket, openPrice, openPrice, curTP, 0, clrAqua);
                        if(mod)
                            Print("[INFO] Breakeven: BUY SL moved to BE. Ticket ", ticket);
                        else
                            Print("[ERROR] Breakeven: Failed to move BUY SL to BE. Ticket ", ticket, " Error: ", GetLastError());
                    }
                    // For SELL
                    if(type == OP_SELL && openPrice - Ask >= beTrigger && (curSL > openPrice || curSL == 0)) {
                        bool mod = OrderModify(ticket, openPrice, openPrice, curTP, 0, clrAqua);
                        if(mod)
                            Print("[INFO] Breakeven: SELL SL moved to BE. Ticket ", ticket);
                        else
                            Print("[ERROR] Breakeven: Failed to move SELL SL to BE. Ticket ", ticket, " Error: ", GetLastError());
                    }
                }

                // --- Trailing Stop Logic ---
                // If UseTrailingProfitAmount is true, trail by profit amount; else, trail by points/percent
                if(UseTrailingProfitAmount && TrailingProfitTrigger > 0 && TrailingProfitStep > 0 && (type == OP_BUY || type == OP_SELL)) {
                    if(profit >= TrailingProfitTrigger) {
                        double newSL = 0;
                        if(type == OP_BUY) {
                            newSL = openPrice + ((profit - TrailingProfitStep) / lots) / MarketInfo(Symbol(), MODE_TICKVALUE) * Point;
                            if(Bid > newSL && (curSL < newSL || curSL == 0)) {
                                bool mod = OrderModify(ticket, openPrice, newSL, curTP, 0, clrGreen);
                                if(mod)
                                    Print("[INFO] TrailingStop (profit): BUY SL trailed. Ticket ", ticket);
                                else
                                    Print("[ERROR] TrailingStop (profit): Failed to trail BUY SL. Ticket ", ticket, " Error: ", GetLastError());
                            }
                        } else if(type == OP_SELL) {
                            newSL = openPrice - ((profit - TrailingProfitStep) / lots) / MarketInfo(Symbol(), MODE_TICKVALUE) * Point;
                            if(Ask < newSL && (curSL > newSL || curSL == 0)) {
                                bool mod = OrderModify(ticket, openPrice, newSL, curTP, 0, clrGreen);
                                if(mod)
                                    Print("[INFO] TrailingStop (profit): SELL SL trailed. Ticket ", ticket);
                                else
                                    Print("[ERROR] TrailingStop (profit): Failed to trail SELL SL. Ticket ", ticket, " Error: ", GetLastError());
                            }
                        }
                    }
                } else if(UseTrailingStop && (type == OP_BUY || type == OP_SELL)) {
                    double trailDist = 0;
                    if(UseTrailingStopPercent && TrailingStopPercent > 0) {
                        trailDist = openPrice * (TrailingStopPercent / 100.0);
                    } else if(TrailingStopPoints > 0) {
                        trailDist = TrailingStopPoints * Point;
                    }
                    // For BUY
                    if(type == OP_BUY && Bid - openPrice > trailDist && trailDist > 0) {
                        double newSL = Bid - trailDist;
                        if(newSL > curSL + Point) { // Only move SL forward
                            bool mod = OrderModify(ticket, openPrice, newSL, curTP, 0, clrGreen);
                            if(mod)
                                Print("[INFO] TrailingStop: BUY SL trailed. Ticket ", ticket);
                            else
                                Print("[ERROR] TrailingStop: Failed to trail BUY SL. Ticket ", ticket, " Error: ", GetLastError());
                        }
                    }
                    // For SELL
                    if(type == OP_SELL && openPrice - Ask > trailDist && trailDist > 0) {
                        double newSL = Ask + trailDist;
                        if(newSL < curSL - Point || curSL == 0) { // Only move SL forward
                            bool mod = OrderModify(ticket, openPrice, newSL, curTP, 0, clrGreen);
                            if(mod)
                                Print("[INFO] TrailingStop: SELL SL trailed. Ticket ", ticket);
                            else
                                Print("[ERROR] TrailingStop: Failed to trail SELL SL. Ticket ", ticket, " Error: ", GetLastError());
                        }
                    }
                }
            }
        }
    }
}
